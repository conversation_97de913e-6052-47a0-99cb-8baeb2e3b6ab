import 'package:flutter/material.dart';
import 'package:intl/intl.dart';
import '../models/trial.dart';
import '../theme/app_theme.dart';

class TrialCard extends StatelessWidget {
  final Trial trial;
  final VoidCallback? onTap;
  final VoidCallback? onCancel;
  final VoidCallback? onSnooze;

  const TrialCard({
    super.key,
    required this.trial,
    this.onTap,
    this.onCancel,
    this.onSnooze,
  });

  @override
  Widget build(BuildContext context) {
    return Card(
      margin: const EdgeInsets.symmetric(horizontal: AppSpacing.md, vertical: AppSpacing.sm),
      child: InkWell(
        onTap: onTap,
        borderRadius: BorderRadius.circular(AppBorderRadius.lg),
        child: Padding(
          padding: const EdgeInsets.all(AppSpacing.md),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Row(
                children: [
                  // Service logo/icon
                  Container(
                    width: 48,
                    height: 48,
                    decoration: BoxDecoration(
                      borderRadius: BorderRadius.circular(AppBorderRadius.md),
                      color: _getUrgencyColor().withOpacity(0.1),
                    ),
                    child: trial.logoUrl != null
                        ? ClipRRect(
                            borderRadius: BorderRadius.circular(AppBorderRadius.md),
                            child: Image.network(
                              trial.logoUrl!,
                              fit: BoxFit.cover,
                              errorBuilder: (context, error, stackTrace) => _buildDefaultIcon(),
                            ),
                          )
                        : _buildDefaultIcon(),
                  ),
                  const SizedBox(width: AppSpacing.md),
                  
                  // Service info
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          trial.serviceName,
                          style: AppTextStyles.heading3,
                          maxLines: 1,
                          overflow: TextOverflow.ellipsis,
                        ),
                        const SizedBox(height: AppSpacing.xs),
                        Text(
                          _getCategoryDisplayName(),
                          style: AppTextStyles.bodySmall.copyWith(
                            color: AppTheme.textSecondary,
                          ),
                        ),
                      ],
                    ),
                  ),
                  
                  // Urgency indicator
                  Container(
                    padding: const EdgeInsets.symmetric(
                      horizontal: AppSpacing.sm,
                      vertical: AppSpacing.xs,
                    ),
                    decoration: BoxDecoration(
                      color: _getUrgencyColor(),
                      borderRadius: BorderRadius.circular(AppBorderRadius.circular),
                    ),
                    child: Text(
                      _getUrgencyText(),
                      style: AppTextStyles.caption.copyWith(
                        color: Colors.white,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                  ),
                ],
              ),
              
              const SizedBox(height: AppSpacing.md),
              
              // Trial details
              Row(
                children: [
                  Expanded(
                    child: _buildDetailItem(
                      icon: Icons.calendar_today,
                      label: 'Expires',
                      value: _getExpiryText(),
                    ),
                  ),
                  Expanded(
                    child: _buildDetailItem(
                      icon: Icons.attach_money,
                      label: 'Monthly Cost',
                      value: '\$${trial.monthlyCost.toStringAsFixed(2)}',
                    ),
                  ),
                ],
              ),
              
              if (trial.status == TrialStatus.active) ...[
                const SizedBox(height: AppSpacing.md),
                
                // Action buttons
                Row(
                  children: [
                    Expanded(
                      child: OutlinedButton.icon(
                        onPressed: onSnooze,
                        icon: const Icon(Icons.snooze, size: 16),
                        label: const Text('Snooze'),
                        style: OutlinedButton.styleFrom(
                          padding: const EdgeInsets.symmetric(vertical: AppSpacing.sm),
                        ),
                      ),
                    ),
                    const SizedBox(width: AppSpacing.sm),
                    Expanded(
                      flex: 2,
                      child: ElevatedButton.icon(
                        onPressed: onCancel,
                        icon: const Icon(Icons.cancel, size: 16),
                        label: const Text('Cancel Trial'),
                        style: ElevatedButton.styleFrom(
                          backgroundColor: _getUrgencyColor(),
                          padding: const EdgeInsets.symmetric(vertical: AppSpacing.sm),
                        ),
                      ),
                    ),
                  ],
                ),
              ],
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildDefaultIcon() {
    return Icon(
      Icons.subscriptions,
      color: _getUrgencyColor(),
      size: 24,
    );
  }

  Widget _buildDetailItem({
    required IconData icon,
    required String label,
    required String value,
  }) {
    return Row(
      children: [
        Icon(
          icon,
          size: 16,
          color: AppTheme.textSecondary,
        ),
        const SizedBox(width: AppSpacing.xs),
        Expanded(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                label,
                style: AppTextStyles.bodySmall.copyWith(
                  color: AppTheme.textSecondary,
                ),
              ),
              Text(
                value,
                style: AppTextStyles.bodyMedium.copyWith(
                  fontWeight: FontWeight.w600,
                ),
                maxLines: 1,
                overflow: TextOverflow.ellipsis,
              ),
            ],
          ),
        ),
      ],
    );
  }

  Color _getUrgencyColor() {
    switch (trial.urgencyLevel) {
      case UrgencyLevel.high:
        return AppTheme.urgentRed;
      case UrgencyLevel.medium:
        return AppTheme.warningOrange;
      case UrgencyLevel.low:
        return AppTheme.cautionYellow;
      case UrgencyLevel.none:
        return AppTheme.primaryGreen;
    }
  }

  String _getUrgencyText() {
    if (trial.status != TrialStatus.active) {
      return trial.status.name.toUpperCase();
    }

    final daysRemaining = trial.daysRemaining;
    if (daysRemaining < 0) {
      return 'EXPIRED';
    } else if (daysRemaining == 0) {
      return 'TODAY';
    } else if (daysRemaining == 1) {
      return '1 DAY';
    } else {
      return '$daysRemaining DAYS';
    }
  }

  String _getExpiryText() {
    if (trial.status != TrialStatus.active) {
      switch (trial.status) {
        case TrialStatus.cancelled:
          return trial.cancelledDate != null
              ? DateFormat('MMM d, y').format(trial.cancelledDate!)
              : 'Cancelled';
        case TrialStatus.expired:
          return 'Expired';
        case TrialStatus.converted:
          return 'Converted';
        default:
          return '';
      }
    }

    final daysRemaining = trial.daysRemaining;
    if (daysRemaining < 0) {
      return 'Expired';
    } else if (daysRemaining == 0) {
      return 'Today';
    } else if (daysRemaining == 1) {
      return 'Tomorrow';
    } else {
      return DateFormat('MMM d, y').format(trial.expiryDate);
    }
  }

  String _getCategoryDisplayName() {
    return trial.serviceCategory.split('_').map((word) {
      return word[0].toUpperCase() + word.substring(1);
    }).join(' ');
  }
}
